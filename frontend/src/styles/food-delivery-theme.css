/* BiteBase Intelligence - Food Delivery Themed Design System */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

:root {
  /* BiteBase Brand Colors (Primary) */
  --primary-color: #74C365;
  --primary-dark: #5fa854;
  --primary-light: #e8f5e5;
  --primary-50: #f0f9ee;
  --primary-100: #e8f5e5;
  --primary-200: #c8e6c0;
  --primary-300: #a8d79b;
  --primary-400: #8ed080;
  --primary-500: #74C365;
  --primary-600: #5fa854;
  --primary-700: #4a8d43;
  --primary-800: #357232;
  --primary-900: #205721;

  /* Food Delivery Accent Colors */
  --food-orange: #FF6B35;
  --food-orange-light: #FFE5DC;
  --food-red: #E23D28;
  --food-red-light: #F8E6E3;
  --food-yellow: #F4C431;
  --food-yellow-light: #FEF7E0;
  --food-brown: #8B4513;
  --food-brown-light: #F5F0EA;

  /* Food Category Colors */
  --pizza-color: #FF6B35;
  --burger-color: #8B4513;
  --sushi-color: #E23D28;
  --taco-color: #F4C431;
  --pasta-color: #74C365;
  --dessert-color: #FF69B4;

  /* Neutral Colors (Enhanced) */
  --color-neutral-50: #f9fafb;
  --color-neutral-100: #f3f4f6;
  --color-neutral-200: #e5e7eb;
  --color-neutral-300: #d1d5db;
  --color-neutral-400: #9ca3af;
  --color-neutral-500: #6b7280;
  --color-neutral-600: #4b5563;
  --color-neutral-700: #374151;
  --color-neutral-800: #1f2937;
  --color-neutral-900: #111827;

  /* Dark Theme Colors */
  --dark-bg-primary: #0f1419;
  --dark-bg-secondary: #1a1f2e;
  --dark-bg-tertiary: #2d3748;
  --dark-text-primary: #ffffff;
  --dark-text-secondary: #a0aec0;
  --dark-border: #2d3748;

  /* Semantic Colors */
  --success-color: var(--primary-color);
  --warning-color: var(--food-yellow);
  --error-color: var(--food-red);
  --info-color: #3B82F6;

  /* Typography */
  --font-display: 'Poppins', sans-serif;
  --font-body: 'Inter', sans-serif;

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Border Radius */
  --radius-xs: 0.125rem;
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Food-themed Gradients */
  --gradient-food-warm: linear-gradient(135deg, var(--food-orange) 0%, var(--food-red) 100%);
  --gradient-food-fresh: linear-gradient(135deg, var(--primary-color) 0%, var(--food-yellow) 100%);
  --gradient-food-premium: linear-gradient(135deg, var(--primary-dark) 0%, var(--food-brown) 100%);
  --gradient-delivery: linear-gradient(135deg, var(--primary-color) 0%, var(--food-orange) 50%, var(--food-yellow) 100%);

  /* Animation Variables */
  --animation-speed-fast: 0.15s;
  --animation-speed-normal: 0.3s;
  --animation-speed-slow: 0.5s;
  --animation-ease: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Dark theme overrides */
[data-theme="dark"] {
  --bg-primary: var(--dark-bg-primary);
  --bg-secondary: var(--dark-bg-secondary);
  --bg-tertiary: var(--dark-bg-tertiary);
  --text-primary: var(--dark-text-primary);
  --text-secondary: var(--dark-text-secondary);
  --border-color: var(--dark-border);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-body);
  background-color: var(--color-neutral-50);
  color: var(--color-neutral-900);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  overflow-x: hidden;
}

/* Typography System */
.text-display-xl {
  font-family: var(--font-display);
  font-size: 4.5rem;
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.text-display-lg {
  font-family: var(--font-display);
  font-size: 3.75rem;
  font-weight: 700;
  line-height: 1.1;
}

.text-display-md {
  font-family: var(--font-display);
  font-size: 3rem;
  font-weight: 600;
  line-height: 1.2;
}

.text-heading-xl {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
}

.text-heading-lg {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
}

.text-heading-md {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
}

.text-body-lg {
  font-size: 1.125rem;
  line-height: 1.6;
}

.text-body-md {
  font-size: 1rem;
  line-height: 1.5;
}

.text-body-sm {
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Food-themed Button System */
.btn-food-primary {
  background: var(--gradient-food-fresh);
  color: white;
  border: none;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-xl);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--animation-speed-normal) var(--animation-ease);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
}

.btn-food-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-food-primary:active {
  transform: translateY(0);
}

.btn-food-secondary {
  background: white;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-xl);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--animation-speed-normal) var(--animation-ease);
}

.btn-food-secondary:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.btn-delivery {
  background: var(--gradient-delivery);
  color: white;
  border: none;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-full);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--animation-speed-normal) var(--animation-bounce);
  box-shadow: var(--shadow-lg);
}

.btn-delivery:hover {
  transform: scale(1.05) translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Food-themed Card System */
.food-card {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  transition: all var(--animation-speed-normal) var(--animation-ease);
  border: 1px solid var(--color-neutral-200);
  position: relative;
  overflow: hidden;
}

.food-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.food-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-delivery);
  opacity: 0;
  transition: opacity var(--animation-speed-normal);
}

.food-card:hover::before {
  opacity: 1;
}

.menu-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--animation-speed-normal) var(--animation-ease);
  border-left: 4px solid var(--primary-color);
}

.menu-card:hover {
  transform: translateX(4px);
  box-shadow: var(--shadow-lg);
  border-left-color: var(--food-orange);
}

.restaurant-card {
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--animation-speed-normal) var(--animation-ease);
  position: relative;
}

.restaurant-card:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-xl);
}

/* Food Category Badges */
.badge-pizza {
  background: var(--pizza-color);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
}

.badge-burger {
  background: var(--burger-color);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
}

.badge-sushi {
  background: var(--sushi-color);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
}

.badge-healthy {
  background: var(--primary-color);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
}

/* Food-themed Animations */
@keyframes foodFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(1deg); }
  50% { transform: translateY(-20px) rotate(0deg); }
  75% { transform: translateY(-10px) rotate(-1deg); }
}

@keyframes deliveryBounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20px); }
  60% { transform: translateY(-10px); }
}

@keyframes plateSpinIn {
  from {
    transform: rotate(-180deg) scale(0);
    opacity: 0;
  }
  to {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
}

@keyframes steamRise {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(-20px);
    opacity: 0;
  }
}

.animate-food-float {
  animation: foodFloat 6s ease-in-out infinite;
}

.animate-delivery-bounce {
  animation: deliveryBounce 2s infinite;
}

.animate-plate-spin {
  animation: plateSpinIn 0.8s var(--animation-bounce);
}

.animate-steam {
  animation: steamRise 3s linear infinite;
}

/* Food-themed Utility Classes */
.food-icon {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-lg);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  background: var(--gradient-food-fresh);
  transition: all var(--animation-speed-normal);
}

.food-icon:hover {
  transform: scale(1.1) rotate(5deg);
}

.delivery-status {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: 500;
}

.delivery-status.preparing {
  background: var(--food-yellow-light);
  color: var(--food-brown);
}

.delivery-status.cooking {
  background: var(--food-orange-light);
  color: var(--food-orange);
}

.delivery-status.ready {
  background: var(--primary-light);
  color: var(--primary-dark);
}

.delivery-status.delivered {
  background: var(--primary-100);
  color: var(--primary-900);
}

/* Food-themed Gradients */
.gradient-warm-food {
  background: var(--gradient-food-warm);
}

.gradient-fresh-food {
  background: var(--gradient-food-fresh);
}

.gradient-premium-food {
  background: var(--gradient-food-premium);
}

.gradient-delivery-hero {
  background: var(--gradient-delivery);
}

/* Interactive Food Elements */
.food-particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  animation: foodFloat 8s linear infinite;
}

.pizza-slice {
  background: var(--pizza-color);
  width: 8px;
  height: 8px;
}

.burger-layer {
  background: var(--burger-color);
  width: 6px;
  height: 6px;
}

.taco-shell {
  background: var(--taco-color);
  width: 10px;
  height: 6px;
  border-radius: 0 0 50% 50%;
}

/* Responsive Food Theme */
@media (max-width: 768px) {
  .text-display-xl { font-size: 3rem; }
  .text-display-lg { font-size: 2.5rem; }
  .text-display-md { font-size: 2rem; }
  
  .btn-food-primary,
  .btn-food-secondary,
  .btn-delivery {
    padding: var(--space-sm) var(--space-lg);
  }
  
  .food-card {
    margin: var(--space-sm);
  }
}

/* Glass Morphism Effects for Food Elements */
.glass-food-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-menu-overlay {
  background: rgba(116, 195, 101, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(116, 195, 101, 0.2);
}

/* Food-themed Loading States */
.loading-food {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--primary-light);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-delivery {
  display: inline-flex;
  gap: var(--space-xs);
}

.loading-delivery-dot {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  animation: deliveryBounce 1.4s infinite ease-in-out;
}

.loading-delivery-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-delivery-dot:nth-child(3) {
  animation-delay: 0.4s;
}