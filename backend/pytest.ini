[tool:pytest]
minversion = 6.0
addopts = 
    -ra
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --junit-xml=test-results.xml
    --tb=short
    --disable-warnings
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    performance: marks tests as performance tests
    security: marks tests as security tests
    api: marks tests as API tests
    collaboration: marks tests as collaboration tests
    asyncio: marks tests as asyncio tests
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
asyncio_mode = auto