"""
BiteBase Intelligence Schemas
Import all Pydantic schemas for API validation
"""

from .restaurant import (
    LocationData,
    RestaurantBase,
    RestaurantCreate,
    RestaurantUpdate,
    RestaurantResponse,
    RestaurantSearchParams,
    RestaurantListResponse,
    MenuItemBase,
    MenuItemCreate,
    MenuItemResponse,
    RestaurantReviewBase,
    RestaurantReviewResponse
)

from .dashboard import (
    GridPosition,
    ResponsiveBreakpoints,
    GridLayoutConfig,
    DashboardColors,
    TypographyFontSize,
    TypographyFontWeight,
    DashboardTypography,
    DashboardSpacing,
    DashboardBorderRadius,
    DashboardTheme,
    AccessibilitySettings,
    DashboardSettings,
    WidgetConfig,
    DashboardWidgetBase,
    DashboardWidgetCreate,
    DashboardWidgetUpdate,
    DashboardWidgetResponse,
    DashboardBase,
    DashboardCreate,
    DashboardUpdate,
    DashboardResponse,
    DashboardListResponse,
    DashboardSearchParams,
    ExportOptions,
    ExportRequest,
    ExportResponse,
    ShareSettings,
    ShareRequest,
    ShareResponse,
    DuplicateRequest
)

from .widget import (
    WidgetTemplateBase,
    WidgetTemplateCreate,
    WidgetTemplateUpdate,
    WidgetTemplateResponse,
    DataSourceConnectionConfig,
    DataSourceQueryConfig,
    WidgetDataSourceBase,
    WidgetDataSourceCreate,
    WidgetDataSourceUpdate,
    WidgetDataSourceResponse,
    WidgetInteractionCreate,
    WidgetInteractionResponse,
    WidgetCacheResponse,
    AlertConditionConfig,
    NotificationConfig,
    WidgetAlertBase,
    WidgetAlertCreate,
    WidgetAlertUpdate,
    WidgetAlertResponse,
    WidgetCommentBase,
    WidgetCommentCreate,
    WidgetCommentUpdate,
    WidgetCommentResponse,
    WidgetTemplateListResponse,
    WidgetDataSourceListResponse,
    WidgetInteractionListResponse
)

__all__ = [
    # Restaurant schemas
    "LocationData",
    "RestaurantBase",
    "RestaurantCreate",
    "RestaurantUpdate",
    "RestaurantResponse",
    "RestaurantSearchParams",
    "RestaurantListResponse",
    "MenuItemBase",
    "MenuItemCreate",
    "MenuItemResponse",
    "RestaurantReviewBase",
    "RestaurantReviewResponse",
    
    # Dashboard schemas
    "GridPosition",
    "ResponsiveBreakpoints",
    "GridLayoutConfig",
    "DashboardColors",
    "TypographyFontSize",
    "TypographyFontWeight",
    "DashboardTypography",
    "DashboardSpacing",
    "DashboardBorderRadius",
    "DashboardTheme",
    "AccessibilitySettings",
    "DashboardSettings",
    "WidgetConfig",
    "DashboardWidgetBase",
    "DashboardWidgetCreate",
    "DashboardWidgetUpdate",
    "DashboardWidgetResponse",
    "DashboardBase",
    "DashboardCreate",
    "DashboardUpdate",
    "DashboardResponse",
    "DashboardListResponse",
    "DashboardSearchParams",
    "ExportOptions",
    "ExportRequest",
    "ExportResponse",
    "ShareSettings",
    "ShareRequest",
    "ShareResponse",
    "DuplicateRequest",
    
    # Widget schemas
    "WidgetTemplateBase",
    "WidgetTemplateCreate",
    "WidgetTemplateUpdate",
    "WidgetTemplateResponse",
    "DataSourceConnectionConfig",
    "DataSourceQueryConfig",
    "WidgetDataSourceBase",
    "WidgetDataSourceCreate",
    "WidgetDataSourceUpdate",
    "WidgetDataSourceResponse",
    "WidgetInteractionCreate",
    "WidgetInteractionResponse",
    "WidgetCacheResponse",
    "AlertConditionConfig",
    "NotificationConfig",
    "WidgetAlertBase",
    "WidgetAlertCreate",
    "WidgetAlertUpdate",
    "WidgetAlertResponse",
    "WidgetCommentBase",
    "WidgetCommentCreate",
    "WidgetCommentUpdate",
    "WidgetCommentResponse",
    "WidgetTemplateListResponse",
    "WidgetDataSourceListResponse",
    "WidgetInteractionListResponse"
]