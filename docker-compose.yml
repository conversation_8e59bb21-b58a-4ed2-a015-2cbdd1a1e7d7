# BiteBase Intelligence - Development Docker Compose
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: bitebase-postgres
    environment:
      POSTGRES_DB: bitebase_intelligence
      POSTGRES_USER: bitebase_user
      POSTGRES_PASSWORD: bitebase_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - bitebase-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U bitebase_user -d bitebase_intelligence"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: bitebase-redis
    command: redis-server --appendonly yes --requirepass redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - bitebase-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: bitebase-backend
    environment:
      - DATABASE_URL=**********************************************************/bitebase_intelligence
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
      - ENVIRONMENT=development
      - DEBUG=true
      - CORS_ORIGINS=http://localhost:3000,http://localhost:80
      - JWT_SECRET_KEY=your-jwt-secret-change-in-production
      - JWT_ALGORITHM=HS256
      - JWT_EXPIRE_MINUTES=30
      - RATE_LIMIT_ENABLED=true
      - MONITORING_ENABLED=true
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    ports:
      - "8000:8000"
    networks:
      - bitebase-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        python -m alembic upgrade head &&
        echo 'Starting application...' &&
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
      "

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: bitebase-frontend
    environment:
      - BACKEND_URL=http://backend:8000
      - ENVIRONMENT=development
      - APP_VERSION=dev
    volumes:
      - frontend_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - bitebase-network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: bitebase-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - bitebase-network
    restart: unless-stopped

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: bitebase-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3001:3000"
    networks:
      - bitebase-network
    depends_on:
      - prometheus
    restart: unless-stopped

  # Log Management - ELK Stack (Optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: bitebase-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - bitebase-network
    restart: unless-stopped
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: bitebase-logstash
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./monitoring/logstash/config:/usr/share/logstash/config
    ports:
      - "5044:5044"
    networks:
      - bitebase-network
    depends_on:
      - elasticsearch
    restart: unless-stopped
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: bitebase-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - bitebase-network
    depends_on:
      - elasticsearch
    restart: unless-stopped
    profiles:
      - logging

# Networks
networks:
  bitebase-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  backend_uploads:
    driver: local
  frontend_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
